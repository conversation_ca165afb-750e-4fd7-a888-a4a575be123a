import XCTest
import Foundation
@testable import ChzzkTV

/// Unit tests for QRCodeURLExtractor
final class QRCodeURLExtractorTests: XCTestCase {
    
    var extractor: QRCodeURLExtractor!
    
    override func setUp() {
        super.setUp()
        extractor = QRCodeURLExtractor()
    }
    
    override func tearDown() {
        extractor = nil
        super.tearDown()
    }
    
    // MARK: - Data URL Decoding Tests
    
    func testDecodeValidDataURL() async {
        // Test with a minimal valid base64 data URL
        let validDataURL = "data:image/jpeg;base64, iVBORw0KGgoAAAANSUhEUgAAAMgAAADIAQAAAACFI5MzAAACFUlEQVR42tVYMY6kQBArRNAhT+Anw8eQQOJjw0/6CYQEaHx2NXM7F2y4vjsC1BpPUCq7XG4C3z3xLyOv4DNhq+dDp04nHYzIAJyPYUN5DqjlFXOcE1CdyNqOZ6eWdLhGVtm7ER3P6Vhai/4Gwh8RMWyVL1Q7In7ICnaKAzjm8Q/mDIgESYXU8+v1oV4D0obyCFV0jQVZ4McE/zyi2ljMPMYjFvWpR9nZIiNCfsqLPSlP6oI/LsDOUn2IGiNlckSPHmCp7zm1IbgCT+liwjqmb7JKdceJxHT0kicaP/FVmwWRGnZpMoZLwwppdB19SDoFG9OcIk9F/uVEOJg7R5QkrZEbo4DO7UQurQgKdZJdhPiJ2689iOrIYqiQK0rys9/MeZDkRyPKxqBG8rPjN3MGBCmJWGpuqqKJzf8YEdrjqW0tfsTUPGZ88CGyR9Um06Y8d3rGmx8XMqxjJgfFV9lUqFktw5oQvdOcpIuMUE2oRoTRTcuCJDG+TkfOaavNh3BJLS0zcXXSpu6N7kJ4iZA5IQck+8TuXOFESA10kwj59UPLAu+7jA3JDMvGzMlPzVJ9SObVV7vPySgPJno5qA9pOV7TKZncKUYXCx9yJwfl+A2Z3yD7diJ5o8wBIT/dkcPS+5FFyVUP7UKCgRvRUTcJKmPLm0Q4kczOXJ1dy/FXarQakbdG9W2jthz/edv9eeT//P72LfILeALFpBWQWnoAAAAASUVORK5CYII="
        
        let urls = await extractor.extractURLs(from: validDataURL)
        
        // Since this is just test data and not a real QR code, we expect no URLs
        // But the method should not crash and should return an empty array
        XCTAssertNotNil(urls)
    }
    
    func testDecodeInvalidDataURL() async {
        let invalidDataURL = "invalid-data-url"
        
        let urls = await extractor.extractURLs(from: invalidDataURL)
        
        XCTAssertNil(urls)
    }
    
    func testDecodeEmptyDataURL() async {
        let emptyDataURL = ""
        
        let urls = await extractor.extractURLs(from: emptyDataURL)
        
        XCTAssertNil(urls)
    }
    
    // MARK: - Image Data Tests
    
    func testExtractURLFromEmptyImageData() async {
        let emptyData = Data()
        
        let urls = await extractor.extractURLs(from: emptyData)
        
        XCTAssertNil(urls)
    }
    
    func testExtractURLFromInvalidImageData() async {
        let invalidData = "not image data".data(using: .utf8)!
        
        let urls = await extractor.extractURLs(from: invalidData)
        
        XCTAssertNil(urls)
    }
    
    // MARK: - UIImage Tests
    
    func testExtractURLFromNilImage() async {
        // Create a simple 1x1 white image (not a QR code)
        let size = CGSize(width: 1, height: 1)
        let renderer = UIGraphicsImageRenderer(size: size)
        let whiteImage = renderer.image { context in
            UIColor.white.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
        
        let urls = await extractor.extractURLs(from: whiteImage)
        
        // Should return empty array since it's not a QR code
        XCTAssertNil(urls)
    }
    
    // MARK: - Convenience Method Tests
    
    func testExtractFirstURLFromEmptyResults() async {
        let emptyData = Data()
        
        let firstURL = await extractor.extractFirstURL(from: emptyData)
        
        XCTAssertNil(firstURL)
    }
    
    func testExtractFirstURLFromDataURL() async {
        let invalidDataURL = "invalid-data-url"
        
        let firstURL = await extractor.extractFirstURL(from: invalidDataURL)
        
        XCTAssertNil(firstURL)
    }
    
    // MARK: - Integration Tests
    
    func testQRCodeLoginViewModelIntegration() async {
        let viewModel = await QRCodeLoginViewModel()
        
        // Test extractURL method with invalid data
        let invalidData = Data()
        let extractedURL = await viewModel.extractURL(from: invalidData)
        
        XCTAssertNil(extractedURL)
    }
    
    func testQRCodeLoginViewModelDataURLExtraction() async {
        let viewModel = await QRCodeLoginViewModel()
        
        // Test extractURL method with invalid data URL
        let invalidDataURL = "invalid-data-url"
        let extractedURL = await viewModel.extractURL(from: invalidDataURL)
        
        XCTAssertNil(extractedURL)
    }
    
    // MARK: - Performance Tests
    
    func testPerformanceOfURLExtraction() {
        // Create a simple test image
        let size = CGSize(width: 100, height: 100)
        let renderer = UIGraphicsImageRenderer(size: size)
        let testImage = renderer.image { context in
            UIColor.white.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
        
        measure {
            let expectation = XCTestExpectation(description: "URL extraction performance")
            
            Task {
                _ = await extractor.extractURLs(from: testImage)
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 5.0)
        }
    }
    
    // MARK: - Mock QR Code Tests
    
    func testWithMockQRCodeContainingURL() async {
        // Note: Creating a real QR code programmatically would require additional dependencies
        // In a real test environment, you would use a pre-generated QR code image
        // or mock the Vision framework responses
        
        // For now, we test the error handling path
        let mockImage = createMockQRCodeImage()
        let urls = await extractor.extractURLs(from: mockImage)
        
        // Since this is not a real QR code, expect empty results
        XCTAssertNil(urls)
    }
    
    // MARK: - Helper Methods
    
    private func createMockQRCodeImage() -> UIImage {
        let size = CGSize(width: 200, height: 200)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            // Create a simple black and white pattern that looks like a QR code
            UIColor.white.setFill()
            context.fill(CGRect(origin: .zero, size: size))
            
            UIColor.black.setFill()
            
            // Draw some random squares to simulate QR code pattern
            for _ in 0..<50 {
                let x = CGFloat.random(in: 0..<size.width)
                let y = CGFloat.random(in: 0..<size.height)
                let squareSize = CGFloat.random(in: 5..<15)
                
                context.fill(CGRect(x: x, y: y, width: squareSize, height: squareSize))
            }
        }
    }
}

// MARK: - Test Extensions

extension QRCodeURLExtractorTests {
    
    /// Test that the extractor handles concurrent requests properly
    func testConcurrentExtractions() async {
        let testImage = createMockQRCodeImage()
        
        // Run multiple extractions concurrently
        async let result1 = extractor.extractURLs(from: testImage)
        async let result2 = extractor.extractURLs(from: testImage)
        async let result3 = extractor.extractURLs(from: testImage)
        
        let results = await [result1, result2, result3]
        
        // All should complete without crashing
        XCTAssertEqual(results.count, 3)
        for result in results {
            XCTAssertNil(result)
        }
    }
    
    /// Test memory usage doesn't grow excessively
    func testMemoryUsage() async {
        let testImage = createMockQRCodeImage()
        
        // Run extraction multiple times to check for memory leaks
        for _ in 0..<10 {
            _ = await extractor.extractURLs(from: testImage)
        }
        
        // If we get here without crashing, memory management is likely OK
        XCTAssertTrue(true)
    }
}
