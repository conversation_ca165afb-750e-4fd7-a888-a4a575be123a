import UIKit
import Vision
import OSLog

/// Utility class for extracting URLs from QR code images
class QRCodeURLExtractor {
    
    // MARK: - Properties
    
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.example.ChzzkTV", category: "QRCodeURLExtractor")
    
    // MARK: - Public Methods
    
    /// Extracts URLs from a QR code image
    /// - Parameter image: The UIImage containing the QR code
    /// - Returns: An array of URLs found in the QR code, or empty array if none found
    func extractURLs(from image: UIImage) async -> [URL]? {
        logger.info("Starting URL extraction from QR code image")
        
        guard let cgImage = image.cgImage else {
            logger.error("Failed to get CGImage from UIImage")
            return nil
        }
        
        return await withCheckedContinuation { continuation in
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            
            let request = VNDetectBarcodesRequest { [weak self] request, error in
                if error != nil {
                    continuation.resume(returning: nil)
                    return
                }
                
                let urls = self?.processVisionResults(request.results)
                continuation.resume(returning: urls)
            }
            
            // Configure the request to detect QR codes specifically
            request.symbologies = [.qr]
            
            do {
                try handler.perform([request])
            } catch {
                logger.error("Failed to perform Vision request: \(error.localizedDescription)")
            }
        }
    }
    
    /// Extracts URLs from QR code image data
    /// - Parameter imageData: The raw image data containing the QR code
    /// - Returns: An array of URLs found in the QR code, or empty array if none found
    func extractURLs(from imageData: Data) async -> [URL]? {
        logger.info("Starting URL extraction from QR code image data")
        
        guard let image = UIImage(data: imageData) else {
            logger.error("Failed to create UIImage from data")
            return nil
        }
        
        return await extractURLs(from: image)
    }
    
    /// Extracts URLs from a base64 encoded data URL string
    /// - Parameter dataURLString: The data URL string (e.g., "data:image/jpeg;base64,...")
    /// - Returns: An array of URLs found in the QR code, or empty array if none found
    func extractURLs(from dataURLString: String) async -> [URL]? {
        logger.info("Starting URL extraction from data URL string")
        
        guard let imageData = decodeDataURL(dataURLString) else {
            logger.error("Failed to decode data URL string")
            return nil
        }
        
        return await extractURLs(from: imageData)
    }
    
    // MARK: - Private Methods
    
    /// Processes Vision framework results to extract URLs
    /// - Parameter results: The results from VNDetectBarcodesRequest
    /// - Returns: An array of URLs found in the results
    private func processVisionResults(_ results: [VNObservation]?) -> [URL] {
        guard let results = results else {
            logger.warning("No Vision results to process")
            return []
        }
        
        logger.info("Processing \(results.count) Vision results")
        
        var extractedURLs: [URL] = []
        
        for result in results {
            guard let barcodeObservation = result as? VNBarcodeObservation else {
                logger.debug("Skipping non-barcode observation")
                continue
            }
            
            guard let payloadString = barcodeObservation.payloadStringValue else {
                logger.debug("Barcode observation has no payload string")
                continue
            }
            
            logger.info("Found QR code payload: \(payloadString)")
            
            // Try to create URL from the payload
            if let url = URL(string: payloadString), isValidURL(url) {
                logger.info("Successfully extracted URL: \(url.absoluteString)")
                extractedURLs.append(url)
            } else {
                logger.warning("Payload is not a valid URL: \(payloadString)")
            }
        }
        
        logger.info("Extracted \(extractedURLs.count) URLs from QR code")
        return extractedURLs
    }
    
    /// Validates if a URL is properly formatted
    /// - Parameter url: The URL to validate
    /// - Returns: True if the URL is valid, false otherwise
    private func isValidURL(_ url: URL) -> Bool {
        // Check if URL has a valid scheme (http, https, etc.)
        guard let scheme = url.scheme, !scheme.isEmpty else {
            return false
        }
        
        // Check if URL has a valid host for http/https URLs
        if scheme.lowercased() == "http" || scheme.lowercased() == "https" {
            guard let host = url.host, !host.isEmpty else {
                return false
            }
        }
        
        return true
    }
    
    /// Decodes a data URL string to raw image data
    /// - Parameter dataURLString: The data URL string to decode
    /// - Returns: The decoded image data, or nil if decoding fails
    private func decodeDataURL(_ dataURLString: String) -> Data? {
        guard dataURLString.starts(with: "data:image"),
              let commaIndex = dataURLString.firstIndex(of: ","),
              commaIndex < dataURLString.endIndex else {
            logger.error("Invalid Data URL format")
            return nil
        }
        
        let base64String = String(dataURLString[dataURLString.index(after: commaIndex)...])
            .trimmingCharacters(in: .whitespacesAndNewlines)
        
        logger.debug("Attempting to decode base64 string of length: \(base64String.count)")
        
        guard let decodedData = Data(base64Encoded: base64String) else {
            logger.error("Failed to decode base64 string")
            return nil
        }
        
        logger.info("Successfully decoded \(decodedData.count) bytes of image data")
        return decodedData
    }
}

// MARK: - Convenience Extensions

extension QRCodeURLExtractor {
    
    /// Extracts the first URL from a QR code image (convenience method)
    /// - Parameter image: The UIImage containing the QR code
    /// - Returns: The first URL found, or nil if none found
    func extractFirstURL(from image: UIImage) async -> URL? {
        let urls = await extractURLs(from: image)
        return urls?.first
    }
    
    /// Extracts the first URL from QR code image data (convenience method)
    /// - Parameter imageData: The raw image data containing the QR code
    /// - Returns: The first URL found, or nil if none found
    func extractFirstURL(from imageData: Data) async -> URL? {
        let urls = await extractURLs(from: imageData)
        return urls?.first
    }
    
    /// Extracts the first URL from a base64 encoded data URL string (convenience method)
    /// - Parameter dataURLString: The data URL string to decode and process
    /// - Returns: The first URL found, or nil if none found
    func extractFirstURL(from dataURLString: String) async -> URL? {
        let urls = await extractURLs(from: dataURLString)
        return urls?.first
    }
}
