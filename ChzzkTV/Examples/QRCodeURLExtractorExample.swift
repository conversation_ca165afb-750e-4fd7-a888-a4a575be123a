import UIKit
import SwiftUI

/// Example usage of QRCodeURLExtractor utility class
struct QRCodeURLExtractorExample {
    
    // MARK: - Example Usage
    
    /// Example 1: Extract URL from UIImage
    static func extractURLFromImage() async {
        let extractor = QRCodeURLExtractor()
        
        // Assuming you have a UIImage containing a QR code
        guard let qrCodeImage = UIImage(named: "sample_qr_code") else {
            print("Sample QR code image not found")
            return
        }
        
        // Extract all URLs from the QR code
        if let urls = await extractor.extractURLs(from: qrCodeImage) {
            print("Found \(urls.count) URLs:")
            for (index, url) in urls.enumerated() {
                print("\(index + 1). \(url.absoluteString)")
            }
        } else {
            print("No URLs found in QR code")
        }
        
        // Or extract just the first URL (convenience method)
        if let firstURL = await extractor.extractFirstURL(from: qrCodeImage) {
            print("First URL: \(firstURL.absoluteString)")
        }
    }
    
    /// Example 2: Extract URL from image data
    static func extractURLFromImageData() async {
        let extractor = QRCodeURLExtractor()
        
        // Assuming you have image data from a network request or file
        guard let imageData = loadQRCodeImageData() else {
            print("Failed to load QR code image data")
            return
        }
        
        // Extract URL from raw image data
        if let url = await extractor.extractFirstURL(from: imageData) {
            print("Extracted URL from image data: \(url.absoluteString)")
        } else {
            print("No URL found in image data")
        }
    }
    
    /// Example 3: Extract URL from base64 data URL string
    static func extractURLFromDataURLString() async {
        let extractor = QRCodeURLExtractor()
        
        // Example data URL string (like what you might get from HTML)
        let dataURLString = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
        
        // Extract URL from data URL string
        if let url = await extractor.extractFirstURL(from: dataURLString) {
            print("Extracted URL from data URL: \(url.absoluteString)")
        } else {
            print("No URL found in data URL string")
        }
    }
    
    /// Example 4: Using in a SwiftUI View
    struct QRCodeScannerView: View {
        @State private var extractedURL: URL?
        @State private var isProcessing = false
        private let extractor = QRCodeURLExtractor()
        
        var body: some View {
            VStack(spacing: 20) {
                Text("QR Code URL Extractor Example")
                    .font(.title2)
                    .padding()
                
                if isProcessing {
                    ProgressView("Processing QR Code...")
                        .padding()
                }
                
                if let url = extractedURL {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Extracted URL:")
                            .font(.headline)
                        
                        Text(url.absoluteString)
                            .font(.caption)
                            .foregroundColor(.blue)
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                            .onTapGesture {
                                UIPasteboard.general.string = url.absoluteString
                            }
                        
                        Text("Tap to copy")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
                
                Button("Process Sample QR Code") {
                    processSampleQRCode()
                }
                .disabled(isProcessing)
                .padding()
            }
        }
        
        private func processSampleQRCode() {
            isProcessing = true
            
            Task {
                // In a real app, you would get this from camera or image picker
                if let sampleImage = createSampleQRCode() {
                    let url = await extractor.extractFirstURL(from: sampleImage)
                    
                    await MainActor.run {
                        self.extractedURL = url
                        self.isProcessing = false
                    }
                } else {
                    await MainActor.run {
                        self.isProcessing = false
                    }
                }
            }
        }
        
        private func createSampleQRCode() -> UIImage? {
            // This would normally be a real QR code image
            // For demo purposes, return nil or a placeholder
            return nil
        }
    }
    
    // MARK: - Helper Methods
    
    /// Mock function to simulate loading QR code image data
    private static func loadQRCodeImageData() -> Data? {
        // In a real app, this might load from:
        // - Network request
        // - Local file
        // - Camera capture
        // - Photo library
        return nil
    }
    
    /// Example of how to integrate with existing QRCodeLoginViewModel
    static func integrateWithViewModel() async {
        let viewModel = await QRCodeLoginViewModel()
        
        // Example: Extract URL from a data URL string (like from HTML parsing)
        let dataURLString = "data:image/jpeg;base64,..." // Your actual data URL
        
        if let extractedURL = await viewModel.extractURL(from: dataURLString) {
            print("URL extracted via ViewModel: \(extractedURL.absoluteString)")
            
            // The extracted URL is also automatically stored in viewModel.extractedQRCodeURL
            // when using the internal extractURLFromQRCode() method
        }
        
        // Example: Extract URL from image data
        let imageData = Data() // Your actual image data
        if let extractedURL = await viewModel.extractURL(from: imageData) {
            print("URL extracted from image data: \(extractedURL.absoluteString)")
        }
    }
}

// MARK: - Usage Instructions

/*
 How to use QRCodeURLExtractor:
 
 1. **Basic Usage:**
    ```swift
    let extractor = QRCodeURLExtractor()
    let urls = await extractor.extractURLs(from: qrCodeImage)
    ```
 
 2. **With QRCodeLoginViewModel:**
    The utility is already integrated into QRCodeLoginViewModel.
    Access extracted URLs via `viewModel.extractedQRCodeURL`
 
 3. **Supported Input Types:**
    - UIImage containing QR code
    - Raw image Data
    - Base64 data URL strings (e.g., "data:image/jpeg;base64,...")
 
 4. **Error Handling:**
    - Methods return empty arrays or nil if no URLs found
    - Check logs for detailed error information
    - Vision framework handles QR code detection automatically
 
 5. **Performance Notes:**
    - All methods are async and run on background queues
    - Results are delivered on the main actor when needed
    - Vision framework is optimized for real-time processing
 
 6. **Integration with UI:**
    - Use @Published extractedQRCodeURL in view models
    - Display URLs in SwiftUI views with proper formatting
    - Add tap-to-copy functionality for user convenience
 */
